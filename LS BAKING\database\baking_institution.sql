-- Sweet Delights Baking Institution Database
-- MySQL Database Schema for XAMPP
-- 
-- Instructions:
-- 1. Start XAMPP and ensure MySQL is running
-- 2. Open phpMyAdmin (http://localhost/phpmyadmin)
-- 3. Create a new database named 'baking_institution'
-- 4. Import this SQL file or run the queries below

-- <PERSON>reate database
CREATE DATABASE IF NOT EXISTS baking_institution CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE baking_institution;

-- --------------------------------------------------------
-- Table structure for table `contacts`
-- --------------------------------------------------------

CREATE TABLE IF NOT EXISTS `contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `newsletter` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('new','read','replied') DEFAULT 'new',
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `services`
-- --------------------------------------------------------

CREATE TABLE IF NOT EXISTS `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `duration` varchar(50) DEFAULT NULL,
  `level` enum('beginner','intermediate','advanced','professional') DEFAULT 'beginner',
  `image_path` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `gallery`
-- --------------------------------------------------------

CREATE TABLE IF NOT EXISTS `gallery` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `image_path` varchar(255) NOT NULL,
  `category` enum('student-work','classes','events','facilities') NOT NULL,
  `is_featured` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_featured` (`is_featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `enrollments`
-- --------------------------------------------------------

CREATE TABLE IF NOT EXISTS `enrollments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `course_name` varchar(100) NOT NULL,
  `course_price` decimal(10,2) DEFAULT NULL,
  `experience` enum('beginner','some','intermediate','advanced') DEFAULT NULL,
  `special_requests` text DEFAULT NULL,
  `enrollment_date` date DEFAULT NULL,
  `status` enum('pending','confirmed','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_course` (`course_name`),
  KEY `idx_status` (`status`),
  KEY `idx_enrollment_date` (`enrollment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `newsletter_subscribers`
-- --------------------------------------------------------

CREATE TABLE IF NOT EXISTS `newsletter_subscribers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `subscribed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Insert sample data for services
-- --------------------------------------------------------

INSERT INTO `services` (`title`, `description`, `price`, `duration`, `level`, `image_path`) VALUES
('Basic Baking Fundamentals', 'Learn the essential skills and techniques needed to start your baking journey with confidence. This course covers basic mixing methods, measuring techniques, and fundamental recipes.', 299.00, '4 weeks', 'beginner', 'images/courses/basic-baking.jpg'),
('Professional Pastry Arts', 'Master the art of pastry making with advanced techniques and creative design skills. Learn laminated doughs, cream fillings, and decorative techniques.', 599.00, '8 weeks', 'intermediate', 'images/courses/pastry-arts.jpg'),
('Artisan Bread Making', 'Learn traditional and modern bread making techniques from sourdough to specialty breads. Understand fermentation, shaping, and scoring techniques.', 449.00, '6 weeks', 'intermediate', 'images/courses/artisan-bread.jpg'),
('Advanced Cake Decorating', 'Create stunning cake designs with professional decorating techniques and artistic flair. Master buttercream flowers, fondant work, and sugar art.', 399.00, '5 weeks', 'intermediate', 'images/courses/cake-decorating.jpg'),
('Professional Baking Diploma', 'Comprehensive program covering all aspects of professional baking and business management. Includes internship placement and job assistance.', 2999.00, '6 months', 'professional', 'images/courses/professional-diploma.jpg'),
('Specialty Workshops', 'Short intensive workshops on specific techniques like macarons, croissants, and seasonal treats. Perfect for skill enhancement.', 149.00, '1-2 days', 'beginner', 'images/courses/specialty-workshops.jpg');

-- --------------------------------------------------------
-- Insert sample data for gallery
-- --------------------------------------------------------

INSERT INTO `gallery` (`title`, `description`, `image_path`, `category`, `is_featured`) VALUES
('Chocolate Layer Cake', 'A beautiful three-layer chocolate cake with buttercream frosting, created by Sarah during her Basic Baking Course. This was her final project showcasing fundamental cake-making techniques.', 'images/gallery/student-cake1.jpg', 'student-work', 1),
('French Macarons', 'Perfectly crafted French macarons in various flavors by Mike from our Pastry Arts Course. These delicate treats showcase advanced piping and flavor techniques.', 'images/gallery/student-pastry1.jpg', 'student-work', 1),
('Artisan Sourdough', 'Beautiful artisan sourdough loaves with perfect crust and crumb structure, baked by Emma in our Artisan Bread Making course.', 'images/gallery/student-bread1.jpg', 'student-work', 0),
('Hands-on Learning', 'Our students actively learning cake decorating techniques in our professional kitchen environment with expert guidance from our instructors.', 'images/gallery/class-session1.jpg', 'classes', 1),
('Pastry Techniques', 'Students mastering the art of laminated dough and pastry folding techniques under the guidance of our expert pastry chef.', 'images/gallery/class-session2.jpg', 'classes', 0),
('Bread Workshop', 'A hands-on bread making workshop where students learn traditional kneading and shaping techniques for artisan breads.', 'images/gallery/class-session3.jpg', 'classes', 0),
('Graduation Day', 'A proud moment as our students receive their certificates after completing their baking courses and demonstrating their newly acquired skills.', 'images/gallery/graduation1.jpg', 'events', 1),
('Annual Baking Contest', 'Our annual baking competition where students showcase their creativity and skills in a friendly competitive environment.', 'images/gallery/competition1.jpg', 'events', 0),
('Holiday Workshop', 'A special holiday workshop where students learn to create festive treats and seasonal decorations for the holiday season.', 'images/gallery/workshop1.jpg', 'events', 0),
('Professional Kitchen', 'Our state-of-the-art professional kitchen equipped with commercial-grade ovens, mixers, and all the tools needed for professional baking education.', 'images/gallery/kitchen1.jpg', 'facilities', 1),
('Theory Classroom', 'Our modern classroom where students learn the theory behind baking science, nutrition, and business aspects of the baking industry.', 'images/gallery/classroom1.jpg', 'facilities', 0),
('Professional Equipment', 'Industry-standard baking equipment including spiral mixers, proof cabinets, and specialized tools that students will encounter in professional kitchens.', 'images/gallery/equipment1.jpg', 'facilities', 0);

-- --------------------------------------------------------
-- Insert sample contact data (for testing)
-- --------------------------------------------------------

INSERT INTO `contacts` (`first_name`, `last_name`, `email`, `phone`, `subject`, `message`, `newsletter`) VALUES
('John', 'Doe', '<EMAIL>', '******-0123', 'general', 'I am interested in learning more about your baking courses. Could you please send me more information?', 1),
('Jane', 'Smith', '<EMAIL>', '******-0124', 'enrollment', 'I would like to enroll in the Basic Baking Fundamentals course. When is the next session starting?', 0),
('Mike', 'Johnson', '<EMAIL>', '******-0125', 'facilities', 'I would like to schedule a tour of your facilities. Are tours available on weekends?', 1);

-- --------------------------------------------------------
-- Insert sample enrollment data (for testing)
-- --------------------------------------------------------

INSERT INTO `enrollments` (`first_name`, `last_name`, `email`, `phone`, `course_name`, `course_price`, `experience`, `enrollment_date`, `status`) VALUES
('Sarah', 'Wilson', '<EMAIL>', '******-0126', 'Basic Baking Fundamentals', 299.00, 'beginner', '2024-01-15', 'confirmed'),
('David', 'Brown', '<EMAIL>', '******-0127', 'Professional Pastry Arts', 599.00, 'some', '2024-01-20', 'pending'),
('Emma', 'Davis', '<EMAIL>', '******-0128', 'Artisan Bread Making', 449.00, 'intermediate', '2024-01-25', 'confirmed');

-- --------------------------------------------------------
-- Insert sample newsletter subscribers
-- --------------------------------------------------------

INSERT INTO `newsletter_subscribers` (`email`) VALUES
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>');

-- --------------------------------------------------------
-- Create views for reporting (optional)
-- --------------------------------------------------------

-- View for enrollment summary
CREATE OR REPLACE VIEW enrollment_summary AS
SELECT 
    course_name,
    COUNT(*) as total_enrollments,
    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_enrollments,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_enrollments,
    AVG(course_price) as average_price,
    MIN(created_at) as first_enrollment,
    MAX(created_at) as latest_enrollment
FROM enrollments
GROUP BY course_name;

-- View for contact summary
CREATE OR REPLACE VIEW contact_summary AS
SELECT 
    subject,
    COUNT(*) as total_contacts,
    COUNT(CASE WHEN status = 'new' THEN 1 END) as unread_contacts,
    COUNT(CASE WHEN newsletter = 1 THEN 1 END) as newsletter_signups
FROM contacts
GROUP BY subject;

-- --------------------------------------------------------
-- Create stored procedures for common operations (optional)
-- --------------------------------------------------------

DELIMITER //

-- Procedure to get enrollment statistics
CREATE PROCEDURE GetEnrollmentStats()
BEGIN
    SELECT 
        COUNT(*) as total_enrollments,
        COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_enrollments,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_enrollments,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_enrollments,
        SUM(course_price) as total_revenue
    FROM enrollments;
END //

-- Procedure to get popular courses
CREATE PROCEDURE GetPopularCourses()
BEGIN
    SELECT 
        course_name,
        COUNT(*) as enrollment_count,
        AVG(course_price) as average_price
    FROM enrollments
    GROUP BY course_name
    ORDER BY enrollment_count DESC
    LIMIT 10;
END //

DELIMITER ;

-- --------------------------------------------------------
-- Create indexes for better performance
-- --------------------------------------------------------

-- Additional indexes for better query performance
CREATE INDEX idx_contacts_subject_status ON contacts(subject, status);
CREATE INDEX idx_enrollments_course_status ON enrollments(course_name, status);
CREATE INDEX idx_gallery_category_featured ON gallery(category, is_featured);

-- --------------------------------------------------------
-- Grant permissions (adjust as needed for your setup)
-- --------------------------------------------------------

-- For XAMPP default setup, these permissions are usually not needed
-- But in production, you would create a specific user:
-- CREATE USER 'baking_user'@'localhost' IDENTIFIED BY 'secure_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON baking_institution.* TO 'baking_user'@'localhost';
-- FLUSH PRIVILEGES;

-- --------------------------------------------------------
-- Database setup complete
-- --------------------------------------------------------

-- Display setup confirmation
SELECT 'Database setup completed successfully!' as message;
SELECT 'Tables created:' as info;
SHOW TABLES;
