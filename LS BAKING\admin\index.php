<?php
/**
 * Simple Admin Dashboard for Sweet Delights Baking Institution
 * Basic admin interface to manage contacts, enrollments, and content
 */

require_once '../php/config.php';

// Simple authentication (in production, use proper authentication)
session_start();
$admin_password = 'admin123'; // Change this in production!

if (isset($_POST['login'])) {
    if ($_POST['password'] === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
    } else {
        $error = 'Invalid password';
    }
}

if (isset($_POST['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    // Show login form
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Login - Sweet Delights</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #8B4513, #FFD700); min-height: 100vh; }
            .login-container { max-width: 400px; margin: 100px auto; }
            .card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="login-container">
                <div class="card">
                    <div class="card-header text-center bg-primary text-white">
                        <h3>🎂 Sweet Delights Admin</h3>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        <form method="POST">
                            <div class="mb-3">
                                <label for="password" class="form-label">Admin Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" name="login" class="btn btn-primary w-100">Login</button>
                        </form>
                        <div class="mt-3 text-center">
                            <small class="text-muted">Default password: admin123</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Admin is logged in, show dashboard
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get statistics
    $stats = [
        'total_contacts' => 0,
        'unread_contacts' => 0,
        'total_enrollments' => 0,
        'pending_enrollments' => 0,
        'newsletter_subscribers' => 0
    ];
    
    // Contact stats
    $stmt = $conn->prepare("SELECT COUNT(*) FROM contacts");
    $stmt->execute();
    $stats['total_contacts'] = $stmt->fetchColumn();
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM contacts WHERE status = 'new'");
    $stmt->execute();
    $stats['unread_contacts'] = $stmt->fetchColumn();
    
    // Enrollment stats
    $stmt = $conn->prepare("SELECT COUNT(*) FROM enrollments");
    $stmt->execute();
    $stats['total_enrollments'] = $stmt->fetchColumn();
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM enrollments WHERE status = 'pending'");
    $stmt->execute();
    $stats['pending_enrollments'] = $stmt->fetchColumn();
    
    // Newsletter stats
    $stmt = $conn->prepare("SELECT COUNT(*) FROM newsletter_subscribers WHERE is_active = 1");
    $stmt->execute();
    $stats['newsletter_subscribers'] = $stmt->fetchColumn();
    
    // Recent contacts
    $stmt = $conn->prepare("SELECT * FROM contacts ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $recent_contacts = $stmt->fetchAll();
    
    // Recent enrollments
    $stmt = $conn->prepare("SELECT * FROM enrollments ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $recent_enrollments = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Sweet Delights</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --cream-white: #FFF5E1;
            --chocolate-brown: #8B4513;
            --light-pink: #FFD1DC;
            --gold: #FFD700;
        }
        body { background-color: var(--cream-white); }
        .navbar { background: linear-gradient(45deg, var(--chocolate-brown), var(--gold)); }
        .card { border-radius: 15px; box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1); }
        .stat-card { transition: transform 0.3s ease; }
        .stat-card:hover { transform: translateY(-5px); }
        .table th { background-color: var(--light-pink); color: var(--chocolate-brown); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-birthday-cake me-2"></i>
                Sweet Delights Admin
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html" target="_blank">
                    <i class="fas fa-external-link-alt me-1"></i>View Website
                </a>
                <form method="POST" class="d-inline">
                    <button type="submit" name="logout" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-2 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                        <h4><?php echo $stats['total_contacts']; ?></h4>
                        <small>Total Contacts</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-envelope-open fa-2x text-warning mb-2"></i>
                        <h4><?php echo $stats['unread_contacts']; ?></h4>
                        <small>Unread</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-graduation-cap fa-2x text-success mb-2"></i>
                        <h4><?php echo $stats['total_enrollments']; ?></h4>
                        <small>Enrollments</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-info mb-2"></i>
                        <h4><?php echo $stats['pending_enrollments']; ?></h4>
                        <small>Pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-newspaper fa-2x text-secondary mb-2"></i>
                        <h4><?php echo $stats['newsletter_subscribers']; ?></h4>
                        <small>Subscribers</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Contacts -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-envelope me-2"></i>Recent Contacts</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Subject</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_contacts as $contact): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($contact['first_name'] . ' ' . $contact['last_name']); ?></td>
                                        <td><?php echo htmlspecialchars($contact['subject']); ?></td>
                                        <td><?php echo date('M j', strtotime($contact['created_at'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $contact['status'] === 'new' ? 'warning' : 'success'; ?>">
                                                <?php echo ucfirst($contact['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Enrollments -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-graduation-cap me-2"></i>Recent Enrollments</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Course</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_enrollments as $enrollment): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($enrollment['first_name'] . ' ' . $enrollment['last_name']); ?></td>
                                        <td><?php echo htmlspecialchars(substr($enrollment['course_name'], 0, 20)) . '...'; ?></td>
                                        <td><?php echo date('M j', strtotime($enrollment['created_at'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $enrollment['status'] === 'pending' ? 'warning' : 'success'; ?>">
                                                <?php echo ucfirst($enrollment['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="contacts.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-envelope me-2"></i>Manage Contacts
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="enrollments.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-graduation-cap me-2"></i>Manage Enrollments
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="gallery.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-images me-2"></i>Manage Gallery
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="services.php" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-cogs me-2"></i>Manage Services
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-5 mb-3">
            <small class="text-muted">
                Sweet Delights Baking Institution Admin Panel &copy; 2024
            </small>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
