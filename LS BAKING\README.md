# Sweet Delights Baking Institution Website

A fully responsive and visually appealing website for a baking institution built with HTML, CSS, JavaScript, Bootstrap, PHP, and MySQL.

## 🎨 Features

### Frontend
- **Responsive Design**: Mobile-first approach using Bootstrap 5
- **Sweet & Simple Theme**: Warm color palette with creamy white, chocolate brown, light pink, and gold
- **Interactive Components**: 
  - Hero image slider
  - Course enrollment modals
  - Gallery with filtering and modal preview
  - Contact form with validation
  - FAQ accordion
- **Modern Typography**: Playfair Display for headings, Poppins for body text
- **Smooth Animations**: CSS transitions and JavaScript animations

### Backend
- **PHP Backend**: Form handling and database operations
- **MySQL Database**: Structured data storage with proper relationships
- **Email Integration**: Automated confirmation and notification emails
- **Data Validation**: Server-side validation and sanitization
- **Admin Features**: Basic admin functionality for managing content

### Pages
1. **Home** - Hero slider, welcome section, service preview, gallery preview
2. **About Us** - Institution history, mission/vision, team profiles, values
3. **Services** - Course listings with interactive cards, enrollment modal, pricing
4. **Gallery** - Filterable image gallery with modal preview and navigation
5. **Contact** - Contact form, Google Maps integration, FAQ section

## 🛠️ Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript ES6, Bootstrap 5
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Server**: Apache (XAMPP)
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Playfair Display, Poppins)

## 📋 Prerequisites

- XAMPP (Apache + MySQL + PHP)
- Modern web browser
- Text editor (VS Code recommended)

## 🚀 Installation & Setup

### 1. Download and Install XAMPP
- Download XAMPP from [https://www.apachefriends.org/](https://www.apachefriends.org/)
- Install and start Apache and MySQL services

### 2. Setup Project Files
```bash
# Clone or download the project to XAMPP's htdocs folder
# Example path: C:\xampp\htdocs\baking-institution\
```

### 3. Database Setup
1. Open phpMyAdmin: `http://localhost/phpmyadmin`
2. Create a new database named `baking_institution`
3. Import the SQL file: `database/baking_institution.sql`
   - Or run the PHP config file which will auto-create the database

### 4. Configuration
- Update database credentials in `php/config.php` if needed
- Default XAMPP settings should work out of the box

### 5. Access the Website
- Open your browser and navigate to: `http://localhost/baking-institution/`

## 📁 Project Structure

```
baking-institution/
├── index.html              # Home page
├── about.html              # About Us page
├── services.html           # Services/Courses page
├── gallery.html            # Gallery page
├── contact.html            # Contact page
├── css/
│   └── style.css          # Main stylesheet
├── js/
│   ├── main.js            # Main JavaScript functionality
│   ├── services.js        # Services page functionality
│   ├── gallery.js         # Gallery page functionality
│   └── contact.js         # Contact page functionality
├── php/
│   ├── config.php         # Database configuration
│   ├── contact_handler.php # Contact form handler
│   └── enrollment_handler.php # Enrollment form handler
├── database/
│   └── baking_institution.sql # Database schema and sample data
├── images/                # Image assets (add your own)
│   ├── hero/             # Hero slider images
│   ├── gallery/          # Gallery images
│   ├── team/             # Team member photos
│   ├── courses/          # Course images
│   └── about/            # About page images
└── README.md             # This file
```

## 🎯 Key Features Explained

### 1. Responsive Design
- Mobile-first approach with Bootstrap 5 grid system
- Breakpoints optimized for all device sizes
- Touch-friendly navigation and interactions

### 2. Course Enrollment System
- Interactive course cards with hover effects
- Modal-based enrollment form
- Form validation (client and server-side)
- Email confirmations for students and admin

### 3. Gallery System
- Category-based filtering (Student Work, Classes, Events, Facilities)
- Modal preview with navigation
- Lazy loading for performance
- Search functionality

### 4. Contact Management
- Comprehensive contact form
- Google Maps integration
- FAQ section with search
- Auto-save form data (localStorage)

### 5. Database Integration
- Structured MySQL database with proper relationships
- Sample data for demonstration
- Admin views and procedures for reporting
- Newsletter subscription management

## 🎨 Color Palette

- **Creamy White**: #FFF5E1 (backgrounds)
- **Chocolate Brown**: #8B4513 (text and headings)
- **Light Pink**: #FFD1DC (highlights)
- **Gold**: #FFD700 (buttons and accents)
- **Dark Brown**: #5D2F0A (secondary text)

## 📱 Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Customization

### Adding New Courses
1. Add course data to the `services` table in MySQL
2. Add course images to `images/courses/`
3. Update the services page if needed

### Adding Gallery Images
1. Upload images to `images/gallery/`
2. Add entries to the `gallery` table
3. Images will automatically appear in the gallery

### Styling Changes
- Modify `css/style.css` for design changes
- Update CSS variables for color scheme changes
- Bootstrap classes can be overridden as needed

## 📧 Email Configuration

The current setup uses PHP's built-in `mail()` function. For production:

1. **Recommended**: Use PHPMailer with SMTP
2. Configure your email service (Gmail, SendGrid, etc.)
3. Update the `sendEmail()` function in `php/config.php`

## 🔒 Security Considerations

- Input validation and sanitization implemented
- SQL injection protection using prepared statements
- XSS protection with htmlspecialchars()
- CSRF protection recommended for production
- Use HTTPS in production environment

## 🚀 Deployment

### For Production Deployment:
1. Use a proper web hosting service
2. Configure SSL certificate
3. Update database credentials
4. Set up proper email service
5. Enable error logging (disable display_errors)
6. Implement backup strategy

## 🐛 Troubleshooting

### Common Issues:

1. **Database Connection Failed**
   - Ensure MySQL is running in XAMPP
   - Check database credentials in `php/config.php`

2. **Images Not Loading**
   - Add actual images to the `images/` directory
   - Check file paths in HTML and database

3. **Forms Not Working**
   - Ensure Apache is running
   - Check PHP error logs in XAMPP

4. **Styling Issues**
   - Clear browser cache
   - Check CSS file paths
   - Verify Bootstrap CDN links

## 📈 Future Enhancements

- User authentication system
- Online payment integration
- Course scheduling system
- Student dashboard
- Advanced admin panel
- Blog/recipe section
- Multi-language support
- Progressive Web App (PWA) features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👥 Credits

- **Design**: Custom "Sweet & Simple" theme
- **Icons**: Font Awesome
- **Fonts**: Google Fonts
- **Framework**: Bootstrap 5
- **Images**: Add your own high-quality baking images

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Phone: +****************

---

**Sweet Delights Baking Institution** - Where passion meets perfection in the art of baking! 🎂👨‍🍳👩‍🍳
