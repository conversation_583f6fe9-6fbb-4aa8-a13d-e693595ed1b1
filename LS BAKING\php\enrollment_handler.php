<?php
/**
 * Enrollment Form Handler
 * Processes course enrollment submissions and stores them in the database
 */

require_once 'config.php';

// Set headers for AJAX requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Only POST requests are allowed');
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        jsonResponse(false, 'Database connection failed');
    }
    
    // Validate and sanitize input data
    $firstName = sanitizeInput($_POST['first_name'] ?? '');
    $lastName = sanitizeInput($_POST['last_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $courseName = sanitizeInput($_POST['course_name'] ?? '');
    $coursePrice = sanitizeInput($_POST['course_price'] ?? '');
    $experience = sanitizeInput($_POST['experience'] ?? '');
    $specialRequests = sanitizeInput($_POST['special_requests'] ?? '');
    $enrollmentDate = sanitizeInput($_POST['enrollment_date'] ?? '');
    $agreeTerms = isset($_POST['agree_terms']) ? 1 : 0;
    
    // Validation
    $errors = [];
    
    if (empty($firstName) || strlen($firstName) < 2) {
        $errors[] = 'First name must be at least 2 characters long';
    }
    
    if (empty($lastName) || strlen($lastName) < 2) {
        $errors[] = 'Last name must be at least 2 characters long';
    }
    
    if (empty($email) || !validateEmail($email)) {
        $errors[] = 'Please provide a valid email address';
    }
    
    if (empty($phone) || strlen($phone) < 10) {
        $errors[] = 'Please provide a valid phone number';
    }
    
    if (empty($courseName)) {
        $errors[] = 'Course name is required';
    }
    
    if (!$agreeTerms) {
        $errors[] = 'You must agree to the terms and conditions';
    }
    
    // Parse course price
    $coursePriceNumeric = 0;
    if (!empty($coursePrice)) {
        $coursePriceNumeric = floatval(str_replace(['$', ','], '', $coursePrice));
    }
    
    // Validate enrollment date
    if (!empty($enrollmentDate)) {
        $dateObj = DateTime::createFromFormat('Y-m-d', $enrollmentDate);
        if (!$dateObj || $dateObj->format('Y-m-d') !== $enrollmentDate) {
            $errors[] = 'Invalid enrollment date format';
        }
    } else {
        $enrollmentDate = date('Y-m-d'); // Default to today
    }
    
    if (!empty($errors)) {
        jsonResponse(false, implode('. ', $errors));
    }
    
    // Check for duplicate enrollments (same email and course in last 24 hours)
    $stmt = $conn->prepare("
        SELECT id FROM enrollments 
        WHERE email = ? AND course_name = ? AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute([$email, $courseName]);
    
    if ($stmt->rowCount() > 0) {
        jsonResponse(false, 'You have already enrolled in this course recently. Please contact us if you need assistance.');
    }
    
    // Check course availability (if course exists in services table)
    $courseExists = checkCourseAvailability($conn, $courseName);
    
    // Insert enrollment into database
    $stmt = $conn->prepare("
        INSERT INTO enrollments (
            first_name, last_name, email, phone, course_name, course_price, 
            experience, special_requests, enrollment_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $firstName,
        $lastName,
        $email,
        $phone,
        $courseName,
        $coursePriceNumeric,
        $experience,
        $specialRequests,
        $enrollmentDate
    ]);
    
    if (!$result) {
        jsonResponse(false, 'Failed to process your enrollment. Please try again.');
    }
    
    $enrollmentId = $conn->lastInsertId();
    
    // Send confirmation email to student
    $studentEmailSent = sendStudentConfirmation($firstName, $email, $courseName, $coursePriceNumeric, $enrollmentId);
    
    // Send notification email to admin
    $adminEmailSent = sendEnrollmentNotification($firstName, $lastName, $email, $courseName, $phone, $experience, $specialRequests);
    
    // Update enrollment statistics
    updateEnrollmentStats($conn, $courseName);
    
    // Prepare response
    $responseMessage = 'Enrollment submitted successfully! We will contact you within 24 hours to confirm your enrollment and provide payment instructions.';
    
    if (!$studentEmailSent) {
        $responseMessage .= ' Note: Confirmation email could not be sent.';
    }
    
    jsonResponse(true, $responseMessage, [
        'enrollment_id' => $enrollmentId,
        'confirmation_sent' => $studentEmailSent,
        'admin_notified' => $adminEmailSent,
        'course_available' => $courseExists
    ]);
    
} catch (Exception $e) {
    error_log("Enrollment form error: " . $e->getMessage());
    jsonResponse(false, 'An error occurred while processing your enrollment. Please try again.');
}

/**
 * Check if course is available
 */
function checkCourseAvailability($conn, $courseName) {
    try {
        $stmt = $conn->prepare("SELECT id, is_active FROM services WHERE title = ?");
        $stmt->execute([$courseName]);
        $course = $stmt->fetch();
        
        return $course && $course['is_active'];
    } catch (Exception $e) {
        return true; // Assume available if check fails
    }
}

/**
 * Send confirmation email to student
 */
function sendStudentConfirmation($firstName, $email, $courseName, $coursePrice, $enrollmentId) {
    $emailSubject = "Enrollment Confirmation - " . SITE_NAME;
    
    $emailBody = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(45deg, #8B4513, #FFD700); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #FFF5E1; }
            .course-info { background: white; padding: 15px; margin: 15px 0; border-left: 4px solid #FFD700; }
            .footer { padding: 15px; text-align: center; font-size: 12px; color: #666; }
            .next-steps { background: #f0f8ff; padding: 15px; margin: 15px 0; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🎂 Sweet Delights Baking Institution</h1>
                <h2>Enrollment Confirmation</h2>
            </div>
            <div class='content'>
                <h2>Welcome to Sweet Delights, {$firstName}!</h2>
                <p>Thank you for your interest in our baking courses. We have received your enrollment request.</p>
                
                <div class='course-info'>
                    <h3>Course Details:</h3>
                    <p><strong>Course:</strong> {$courseName}</p>
                    <p><strong>Price:</strong> $" . number_format($coursePrice, 2) . "</p>
                    <p><strong>Enrollment ID:</strong> #{$enrollmentId}</p>
                </div>
                
                <div class='next-steps'>
                    <h3>Next Steps:</h3>
                    <ol>
                        <li>Our admissions team will contact you within 24 hours to confirm your enrollment</li>
                        <li>We will provide detailed payment instructions and course schedule</li>
                        <li>You will receive a welcome packet with course materials and preparation guidelines</li>
                        <li>If you have any questions, please don't hesitate to contact us</li>
                    </ol>
                </div>
                
                <p>We're excited to help you on your baking journey!</p>
                <p>Best regards,<br>The Sweet Delights Admissions Team</p>
            </div>
            <div class='footer'>
                <p>Sweet Delights Baking Institution<br>
                123 Baking Street, Sweet City<br>
                Phone: +**************** | Email: <EMAIL></p>
                <p>If you did not request this enrollment, please contact us immediately.</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    $headers = "From: " . SITE_NAME . " Admissions <<EMAIL>>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return sendEmail($email, $emailSubject, $emailBody, $headers);
}

/**
 * Send notification email to admin
 */
function sendEnrollmentNotification($firstName, $lastName, $email, $courseName, $phone, $experience, $specialRequests) {
    $emailSubject = "New Course Enrollment - " . SITE_NAME;
    
    $emailBody = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #8B4513; color: white; padding: 15px; }
            .content { padding: 20px; background: #f9f9f9; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #8B4513; }
            .urgent { background: #ffe6e6; padding: 10px; border-left: 4px solid #ff4444; margin: 15px 0; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>🎓 New Course Enrollment</h2>
            </div>
            <div class='content'>
                <div class='urgent'>
                    <strong>Action Required:</strong> Please contact this student within 24 hours to confirm enrollment.
                </div>
                
                <div class='field'>
                    <span class='label'>Student Name:</span> {$firstName} {$lastName}
                </div>
                <div class='field'>
                    <span class='label'>Email:</span> {$email}
                </div>
                <div class='field'>
                    <span class='label'>Phone:</span> {$phone}
                </div>
                <div class='field'>
                    <span class='label'>Course:</span> {$courseName}
                </div>
                <div class='field'>
                    <span class='label'>Experience Level:</span> " . ucfirst($experience ?: 'Not specified') . "
                </div>
                <div class='field'>
                    <span class='label'>Special Requests:</span><br>
                    " . ($specialRequests ? nl2br(htmlspecialchars($specialRequests)) : 'None') . "
                </div>
                <div class='field'>
                    <span class='label'>Submitted:</span> " . date('Y-m-d H:i:s') . "
                </div>
            </div>
        </div>
    </body>
    </html>
    ";
    
    $headers = "From: " . SITE_NAME . " <" . SITE_EMAIL . ">\r\n";
    $headers .= "Reply-To: {$email}\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return sendEmail(ADMIN_EMAIL, $emailSubject, $emailBody, $headers);
}

/**
 * Update enrollment statistics
 */
function updateEnrollmentStats($conn, $courseName) {
    try {
        // This could be used to track popular courses, enrollment trends, etc.
        // For now, we'll just log it
        error_log("New enrollment for course: " . $courseName);
    } catch (Exception $e) {
        error_log("Error updating enrollment stats: " . $e->getMessage());
    }
}

/**
 * Get enrollment statistics (for admin dashboard)
 */
function getEnrollmentStats($conn) {
    $stats = [];
    
    // Total enrollments
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM enrollments");
    $stmt->execute();
    $stats['total'] = $stmt->fetchColumn();
    
    // Recent enrollments (last 7 days)
    $stmt = $conn->prepare("SELECT COUNT(*) as recent FROM enrollments WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $stmt->execute();
    $stats['recent'] = $stmt->fetchColumn();
    
    // Pending enrollments
    $stmt = $conn->prepare("SELECT COUNT(*) as pending FROM enrollments WHERE status = 'pending'");
    $stmt->execute();
    $stats['pending'] = $stmt->fetchColumn();
    
    // Popular courses
    $stmt = $conn->prepare("
        SELECT course_name, COUNT(*) as count 
        FROM enrollments 
        GROUP BY course_name 
        ORDER BY count DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $stats['popular_courses'] = $stmt->fetchAll();
    
    return $stats;
}

/**
 * Get recent enrollments (for admin dashboard)
 */
function getRecentEnrollments($conn, $limit = 10) {
    $stmt = $conn->prepare("
        SELECT id, first_name, last_name, email, course_name, created_at, status 
        FROM enrollments 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}
?>
