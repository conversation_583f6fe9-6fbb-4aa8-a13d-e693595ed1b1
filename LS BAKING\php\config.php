<?php
/**
 * Database Configuration for Sweet Delights Baking Institution
 * 
 * This file contains the database connection settings for XAMPP/MySQL
 * Make sure XAM<PERSON> is running with Apache and MySQL services
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', ''); // Default XAMPP MySQL password is empty
define('DB_NAME', 'baking_institution');

// Application settings
define('SITE_NAME', 'Sweet Delights Baking Institution');
define('SITE_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('America/New_York');

/**
 * Database Connection Class
 */
class Database {
    private $host = DB_HOST;
    private $username = DB_USERNAME;
    private $password = DB_PASSWORD;
    private $database = DB_NAME;
    private $connection;
    
    /**
     * Get database connection
     */
    public function getConnection() {
        $this->connection = null;
        
        try {
            $this->connection = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->database,
                $this->username,
                $this->password
            );
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
        
        return $this->connection;
    }
    
    /**
     * Create database and tables if they don't exist
     */
    public function initializeDatabase() {
        try {
            // Connect without database name first
            $tempConnection = new PDO(
                "mysql:host=" . $this->host,
                $this->username,
                $this->password
            );
            $tempConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->database . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $tempConnection->exec($sql);
            
            // Now connect to the database
            $this->connection = $this->getConnection();
            
            // Create tables
            $this->createTables();
            
            return true;
        } catch(PDOException $e) {
            echo "Database initialization error: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Create necessary tables
     */
    private function createTables() {
        // Contacts table
        $sql = "CREATE TABLE IF NOT EXISTS contacts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            subject VARCHAR(100) NOT NULL,
            message TEXT NOT NULL,
            newsletter BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('new', 'read', 'replied') DEFAULT 'new'
        )";
        $this->connection->exec($sql);
        
        // Services table
        $sql = "CREATE TABLE IF NOT EXISTS services (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(100) NOT NULL,
            description TEXT,
            price DECIMAL(10,2),
            duration VARCHAR(50),
            level ENUM('beginner', 'intermediate', 'advanced', 'professional') DEFAULT 'beginner',
            image_path VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $this->connection->exec($sql);
        
        // Gallery table
        $sql = "CREATE TABLE IF NOT EXISTS gallery (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(100) NOT NULL,
            description TEXT,
            image_path VARCHAR(255) NOT NULL,
            category ENUM('student-work', 'classes', 'events', 'facilities') NOT NULL,
            is_featured BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $this->connection->exec($sql);
        
        // Enrollments table
        $sql = "CREATE TABLE IF NOT EXISTS enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            course_name VARCHAR(100) NOT NULL,
            course_price DECIMAL(10,2),
            experience ENUM('beginner', 'some', 'intermediate', 'advanced'),
            special_requests TEXT,
            enrollment_date DATE,
            status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $this->connection->exec($sql);
        
        // Newsletter subscribers table
        $sql = "CREATE TABLE IF NOT EXISTS newsletter_subscribers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(100) UNIQUE NOT NULL,
            subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )";
        $this->connection->exec($sql);
        
        // Insert sample data
        $this->insertSampleData();
    }
    
    /**
     * Insert sample data for demonstration
     */
    private function insertSampleData() {
        // Check if services already exist
        $stmt = $this->connection->prepare("SELECT COUNT(*) FROM services");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            // Insert sample services
            $services = [
                [
                    'title' => 'Basic Baking Fundamentals',
                    'description' => 'Learn the essential skills and techniques needed to start your baking journey with confidence.',
                    'price' => 299.00,
                    'duration' => '4 weeks',
                    'level' => 'beginner',
                    'image_path' => 'images/courses/basic-baking.jpg'
                ],
                [
                    'title' => 'Professional Pastry Arts',
                    'description' => 'Master the art of pastry making with advanced techniques and creative design skills.',
                    'price' => 599.00,
                    'duration' => '8 weeks',
                    'level' => 'intermediate',
                    'image_path' => 'images/courses/pastry-arts.jpg'
                ],
                [
                    'title' => 'Artisan Bread Making',
                    'description' => 'Learn traditional and modern bread making techniques from sourdough to specialty breads.',
                    'price' => 449.00,
                    'duration' => '6 weeks',
                    'level' => 'intermediate',
                    'image_path' => 'images/courses/artisan-bread.jpg'
                ],
                [
                    'title' => 'Advanced Cake Decorating',
                    'description' => 'Create stunning cake designs with professional decorating techniques and artistic flair.',
                    'price' => 399.00,
                    'duration' => '5 weeks',
                    'level' => 'intermediate',
                    'image_path' => 'images/courses/cake-decorating.jpg'
                ],
                [
                    'title' => 'Professional Baking Diploma',
                    'description' => 'Comprehensive program covering all aspects of professional baking and business management.',
                    'price' => 2999.00,
                    'duration' => '6 months',
                    'level' => 'professional',
                    'image_path' => 'images/courses/professional-diploma.jpg'
                ]
            ];
            
            $stmt = $this->connection->prepare("
                INSERT INTO services (title, description, price, duration, level, image_path) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($services as $service) {
                $stmt->execute([
                    $service['title'],
                    $service['description'],
                    $service['price'],
                    $service['duration'],
                    $service['level'],
                    $service['image_path']
                ]);
            }
        }
        
        // Check if gallery items already exist
        $stmt = $this->connection->prepare("SELECT COUNT(*) FROM gallery");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            // Insert sample gallery items
            $galleryItems = [
                [
                    'title' => 'Chocolate Layer Cake',
                    'description' => 'A beautiful three-layer chocolate cake with buttercream frosting, created by Sarah during her Basic Baking Course.',
                    'image_path' => 'images/gallery/student-cake1.jpg',
                    'category' => 'student-work'
                ],
                [
                    'title' => 'French Macarons',
                    'description' => 'Perfectly crafted French macarons in various flavors by Mike from our Pastry Arts Course.',
                    'image_path' => 'images/gallery/student-pastry1.jpg',
                    'category' => 'student-work'
                ],
                [
                    'title' => 'Artisan Sourdough',
                    'description' => 'Beautiful artisan sourdough loaves with perfect crust and crumb structure.',
                    'image_path' => 'images/gallery/student-bread1.jpg',
                    'category' => 'student-work'
                ],
                [
                    'title' => 'Hands-on Learning',
                    'description' => 'Our students actively learning cake decorating techniques in our professional kitchen.',
                    'image_path' => 'images/gallery/class-session1.jpg',
                    'category' => 'classes'
                ],
                [
                    'title' => 'Professional Kitchen',
                    'description' => 'Our state-of-the-art professional kitchen equipped with commercial-grade equipment.',
                    'image_path' => 'images/gallery/kitchen1.jpg',
                    'category' => 'facilities'
                ]
            ];
            
            $stmt = $this->connection->prepare("
                INSERT INTO gallery (title, description, image_path, category) 
                VALUES (?, ?, ?, ?)
            ");
            
            foreach ($galleryItems as $item) {
                $stmt->execute([
                    $item['title'],
                    $item['description'],
                    $item['image_path'],
                    $item['category']
                ]);
            }
        }
    }
}

/**
 * Utility Functions
 */

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Validate email address
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Send email (basic implementation)
 */
function sendEmail($to, $subject, $message, $headers = '') {
    // In a production environment, use a proper email service like PHPMailer
    // For now, this is a basic implementation
    
    if (empty($headers)) {
        $headers = "From: " . SITE_EMAIL . "\r\n";
        $headers .= "Reply-To: " . SITE_EMAIL . "\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    }
    
    return mail($to, $subject, $message, $headers);
}

/**
 * Generate response JSON
 */
function jsonResponse($success, $message, $data = null) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Initialize database on first load
$database = new Database();
$database->initializeDatabase();
?>
