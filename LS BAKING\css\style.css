/* Apple-Inspired Modern Design - <PERSON> Classes */

/* Modern Apple-Style Color Palette */
:root {
    /* Primary Colors */
    --primary-black: #1d1d1f;
    --primary-gray: #86868b;
    --secondary-gray: #f5f5f7;
    --light-gray: #fbfbfd;
    --pure-white: #ffffff;

    /* Accent Colors */
    --accent-blue: #007aff;
    --accent-orange: #ff9500;
    --accent-green: #30d158;
    --accent-red: #ff3b30;

    /* Baking Theme Colors */
    --warm-brown: #8b4513;
    --golden-yellow: #ffd700;
    --cream-white: #faf0e6;

    /* Shadows - Apple Style */
    --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-medium: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
    --shadow-large: 0 10px 20px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.10);
    --shadow-xl: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);

    /* Border Radius */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 24px;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Global Styles - Apple Inspired */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: var(--primary-black);
    background: var(--pure-white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Apple-Style Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    color: var(--primary-black);
    letter-spacing: -0.01em;
    line-height: 1.1;
    margin: 0;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); font-weight: 700; }
h2 { font-size: clamp(2rem, 4vw, 3rem); font-weight: 600; }
h3 { font-size: clamp(1.5rem, 3vw, 2.25rem); font-weight: 600; }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.75rem); font-weight: 600; }
h5 { font-size: clamp(1.1rem, 2vw, 1.5rem); font-weight: 600; }
h6 { font-size: clamp(1rem, 1.5vw, 1.25rem); font-weight: 600; }

p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--primary-gray);
    margin: 0;
}

/* Apple-Style Section Styling */
.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1rem;
    color: var(--primary-black);
    text-align: center;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.section-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    color: var(--primary-gray);
    margin-bottom: 4rem;
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
    line-height: 1.5;
}

/* Apple-Style Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

@media (min-width: 768px) {
    .container {
        padding: 0 40px;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 60px;
    }
}

/* Apple-Style Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: all var(--transition-medium);
    padding: 12px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-small);
    padding: 8px 0;
}

.navbar-brand {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-black) !important;
    display: flex;
    align-items: center;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.navbar-brand:hover {
    color: var(--primary-black) !important;
    text-decoration: none;
}

.navbar-brand .logo-img {
    height: 40px;
    width: auto;
    margin-right: 12px;
    transition: all var(--transition-fast);
}

.navbar-brand .brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.navbar-brand .brand-text .main-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-black);
    letter-spacing: -0.01em;
}

.navbar-brand .brand-text .sub-text {
    font-size: 0.85rem;
    font-weight: 400;
    color: var(--primary-gray);
    letter-spacing: 0.01em;
}

.nav-link {
    font-weight: 400;
    color: var(--primary-black) !important;
    margin: 0 8px;
    padding: 8px 12px !important;
    position: relative;
    transition: all var(--transition-fast);
    font-size: 1rem;
    letter-spacing: -0.01em;
    border-radius: var(--radius-small);
    text-decoration: none;
}

.nav-link:hover {
    color: var(--primary-black) !important;
    background: var(--secondary-gray);
    text-decoration: none;
}

.nav-link.active {
    color: var(--accent-blue) !important;
    background: rgba(0, 122, 255, 0.1);
    font-weight: 500;
}

/* Mobile Navigation Toggle */
.navbar-toggler {
    border: none;
    padding: 4px 8px;
    border-radius: var(--radius-small);
    transition: all var(--transition-fast);
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2829, 29, 31, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Apple-Style Hero Section */
.hero-section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    margin-top: 0;
    padding-top: 80px;
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--pure-white) 100%);
}

.carousel-item {
    min-height: 100vh;
}

.hero-slide {
    min-height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

/* Modern Background Images with Subtle Overlay */
.carousel-item:nth-child(1) .hero-slide {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(245, 245, 247, 0.8) 100%),
                url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
}

.carousel-item:nth-child(2) .hero-slide {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(245, 245, 247, 0.8) 100%),
                url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
}

.carousel-item:nth-child(3) .hero-slide {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(245, 245, 247, 0.8) 100%),
                url('https://images.unsplash.com/photo-1574085733277-851d9d856a3a?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
    line-height: 1.1;
    text-align: center;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    color: var(--primary-gray);
    margin-bottom: 2.5rem;
    font-weight: 400;
    max-width: 600px;
    line-height: 1.5;
    text-align: center;
}

/* Apple-Style Buttons */
.btn-primary {
    background: var(--accent-blue);
    border: none;
    color: var(--pure-white);
    font-weight: 500;
    padding: 12px 24px;
    border-radius: var(--radius-large);
    transition: all var(--transition-medium);
    font-size: 1rem;
    letter-spacing: -0.01em;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
    cursor: pointer;
}

.btn-primary:hover {
    background: #0056d3;
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
    color: var(--pure-white);
    text-decoration: none;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-small);
}

.btn-outline-primary {
    border: 1px solid var(--accent-blue);
    color: var(--accent-blue);
    background: transparent;
    font-weight: 500;
    padding: 11px 23px;
    border-radius: var(--radius-large);
    transition: all var(--transition-medium);
    font-size: 1rem;
    letter-spacing: -0.01em;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
    cursor: pointer;
}

.btn-outline-primary:hover {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
    color: var(--pure-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
    text-decoration: none;
}

.btn-outline-light {
    border: 1px solid rgba(29, 29, 31, 0.3);
    color: var(--primary-black);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    font-weight: 500;
    padding: 11px 23px;
    border-radius: var(--radius-large);
    transition: all var(--transition-medium);
    font-size: 1rem;
    letter-spacing: -0.01em;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
    cursor: pointer;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(29, 29, 31, 0.5);
    color: var(--primary-black);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
    text-decoration: none;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.1rem;
    min-height: 52px;
}

/* Apple-Style Welcome Section */
.welcome-section {
    background: var(--pure-white);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.welcome-content h2 {
    color: var(--primary-black);
    margin-bottom: 1.5rem;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    letter-spacing: -0.02em;
}

.welcome-content .lead {
    color: var(--accent-orange);
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    font-weight: 500;
    margin-bottom: 2rem;
}

.welcome-content p {
    color: var(--primary-gray);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.welcome-stats {
    margin-top: 3rem;
}

.stat-number {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--primary-gray);
    font-weight: 500;
    letter-spacing: -0.01em;
}

.welcome-image {
    position: relative;
}

.welcome-image img,
.welcome-image > div {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-large);
    transition: transform var(--transition-medium);
    overflow: hidden;
}

.welcome-image:hover img,
.welcome-image:hover > div {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

/* Apple-Style Services Preview */
.services-preview {
    background: var(--secondary-gray);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.services-preview .section-title {
    color: var(--primary-black);
}

.services-preview .section-subtitle {
    color: var(--primary-gray);
}

.service-card {
    background: var(--pure-white);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    background: var(--pure-white);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-blue);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
    background: var(--accent-orange);
}

.service-icon i {
    font-size: 2rem;
    color: var(--pure-white);
}

.service-card h4 {
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.service-card p {
    color: var(--primary-gray);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Apple-Style Workshop Section */
.workshop-section {
    background: var(--cream-white);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.workshop-section .section-title {
    color: var(--warm-brown);
    margin-bottom: 2rem;
}

.workshop-section .card {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    overflow: hidden;
}

.workshop-section .card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

.workshop-section .card-header {
    background: var(--golden-yellow);
    border: none;
    padding: 1.5rem;
}

.workshop-section .card-body {
    padding: 2rem;
    background: var(--pure-white);
}

.workshop-section .alert {
    border: none;
    border-radius: var(--radius-large);
    padding: 1.5rem;
    margin: 2rem 0;
}

/* Apple-Style Gallery Preview */
.gallery-preview {
    padding: 100px 0;
    background: var(--pure-white);
    position: relative;
    overflow: hidden;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    background: var(--pure-white);
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.gallery-item img,
.gallery-item > div {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: all var(--transition-medium);
    border-radius: var(--radius-xl);
}

.gallery-item:hover img,
.gallery-item:hover > div {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(29, 29, 31, 0.8);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-medium);
    border-radius: var(--radius-xl);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay .btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--primary-black);
    backdrop-filter: blur(20px);
    font-weight: 500;
    border-radius: var(--radius-large);
    padding: 12px 24px;
}

/* Apple-Style Footer */
.footer {
    background: var(--primary-black);
    color: var(--pure-white);
    padding: 60px 0 30px;
    position: relative;
    overflow: hidden;
}

.footer h5,
.footer h6 {
    color: var(--pure-white);
    margin-bottom: 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    letter-spacing: -0.01em;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo .logo-img {
    height: 50px;
    width: auto;
    margin-right: 15px;
    filter: brightness(1.2);
}

.footer-logo .brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.footer-logo .brand-text .main-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--pure-white);
    letter-spacing: -0.01em;
}

.footer-logo .brand-text .sub-text {
    font-size: 0.9rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: -0.01em;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-fast);
    font-size: 0.95rem;
}

.footer-links a:hover {
    color: var(--pure-white);
    text-decoration: none;
}

.contact-info {
    list-style: none;
    padding: 0;
}

.contact-info li {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
    line-height: 1.5;
}

.contact-info i {
    color: var(--accent-blue);
    margin-right: 12px;
    width: 20px;
    margin-top: 2px;
    flex-shrink: 0;
}

.social-links {
    margin-top: 1.5rem;
}

.social-links a {
    display: inline-block;
    width: 44px;
    height: 44px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--pure-white);
    text-align: center;
    line-height: 44px;
    border-radius: var(--radius-medium);
    margin-right: 12px;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.social-links a:hover {
    background-color: var(--accent-blue);
    transform: translateY(-2px);
    color: var(--pure-white);
    text-decoration: none;
}

.newsletter-form .form-control {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-large) 0 0 var(--radius-large);
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--pure-white);
    font-size: 0.95rem;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-form .btn {
    border-radius: 0 var(--radius-large) var(--radius-large) 0;
    padding: 12px 20px;
    border: none;
    background: var(--accent-blue);
    color: var(--pure-white);
    font-weight: 500;
}

.newsletter-form .btn:hover {
    background: #0056d3;
}

/* Apple-Style Page Header */
.page-header {
    background: var(--secondary-gray);
    padding: 120px 0 80px;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.page-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    text-align: center;
}

.page-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    color: var(--primary-gray);
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Apple-Style About Page */
.our-story {
    background: var(--pure-white);
    padding: 100px 0;
}

.story-content .lead {
    color: var(--accent-orange);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.highlight-item {
    text-align: center;
    padding: 2rem 1rem;
    transition: all var(--transition-medium);
}

.highlight-item:hover {
    transform: translateY(-4px);
}

.highlight-item i {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1.5rem;
}

.highlight-item h5 {
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-weight: 600;
}

.highlight-item p {
    font-size: 1rem;
    color: var(--primary-gray);
    line-height: 1.6;
}

.mission-card,
.vision-card {
    background: var(--pure-white);
    padding: 3rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    height: 100%;
    text-align: center;
    transition: all var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.mission-card:hover,
.vision-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-blue);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
}

.mission-card:hover .card-icon,
.vision-card:hover .card-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.card-icon i {
    font-size: 2rem;
    color: var(--pure-white);
}

.value-card {
    padding: 2rem 1rem;
    transition: all var(--transition-medium);
    text-align: center;
}

.value-card:hover {
    transform: translateY(-4px);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: var(--accent-green);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all var(--transition-medium);
}

.value-card:hover .value-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.value-icon i {
    font-size: 1.5rem;
    color: var(--pure-white);
}

.team-card {
    background: var(--pure-white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.team-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.team-image img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: all var(--transition-medium);
}

.team-card:hover .team-image img {
    transform: scale(1.05);
}

.team-info {
    padding: 2rem 1.5rem;
    text-align: center;
}

.team-role {
    color: var(--accent-blue);
    font-weight: 500;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.team-social {
    margin-top: 1.5rem;
}

.team-social a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--secondary-gray);
    color: var(--primary-black);
    text-align: center;
    line-height: 40px;
    border-radius: var(--radius-medium);
    margin: 0 6px;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.team-social a:hover {
    background-color: var(--accent-blue);
    color: var(--pure-white);
    transform: translateY(-2px);
    text-decoration: none;
}

/* Apple-Style Services Page */
.course-categories {
    background: var(--pure-white);
    padding: 100px 0;
}

.category-card {
    background: var(--pure-white);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    text-align: center;
    height: 100%;
    transition: all var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-blue);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
}

.category-card:hover .category-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.category-icon i {
    font-size: 2rem;
    color: var(--pure-white);
}

.category-features {
    list-style: none;
    padding: 0;
    margin-top: 2rem;
    text-align: left;
}

.category-features li {
    padding: 0.8rem 0;
    color: var(--primary-gray);
    position: relative;
    padding-left: 2rem;
    font-size: 1rem;
    line-height: 1.5;
}

.category-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-green);
    font-weight: 600;
    font-size: 1.1rem;
}

.course-card {
    background: var(--pure-white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.course-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.course-card.featured {
    border: 2px solid var(--accent-blue);
}

.course-image {
    position: relative;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: all var(--transition-medium);
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.course-level {
    position: absolute;
    top: 16px;
    left: 16px;
    background: var(--primary-black);
    color: var(--pure-white);
    padding: 6px 16px;
    border-radius: var(--radius-large);
    font-size: 0.85rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.featured-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--accent-orange);
    color: var(--pure-white);
    padding: 6px 16px;
    border-radius: var(--radius-large);
    font-size: 0.85rem;
    font-weight: 600;
}

.course-content {
    padding: 2rem 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.course-content h4 {
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-weight: 600;
}

.course-content p {
    color: var(--primary-gray);
    margin-bottom: 1.5rem;
    flex-grow: 1;
    line-height: 1.6;
}

.course-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--dark-brown);
}

.detail-item i {
    color: var(--gold);
    margin-right: 10px;
    width: 16px;
}

.course-price {
    text-align: center;
    margin-bottom: 1.5rem;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--chocolate-brown);
}

.price-note {
    font-size: 0.9rem;
    color: var(--dark-brown);
    display: block;
}

.feature-item {
    padding: 2rem 1rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--light-pink);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--chocolate-brown);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    background: linear-gradient(45deg, var(--chocolate-brown), var(--gold));
    color: var(--white);
    border-radius: 15px 15px 0 0;
}

.modal-title {
    color: var(--white);
}

.btn-close {
    filter: invert(1);
}

.form-label {
    color: var(--chocolate-brown);
    font-weight: 500;
}

.form-control,
.form-select {
    border: 2px solid var(--cream-white);
    border-radius: 10px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--gold);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

/* Gallery Page Styles */
.gallery-filter {
    background-color: var(--light-gray);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-buttons .btn {
    margin: 5px;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-buttons .btn.active {
    background-color: var(--chocolate-brown);
    border-color: var(--chocolate-brown);
    color: var(--white);
}

.gallery-grid {
    background-color: var(--white);
}

.gallery-card {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.gallery-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-card:hover img {
    transform: scale(1.1);
}

/* Portrait Gallery Card Styles */
.portrait-card {
    aspect-ratio: 3/4;
    min-height: 400px;
}

.portrait-card img,
.portrait-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
}

.portrait-card .text-center {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Responsive portrait adjustments */
@media (max-width: 768px) {
    .portrait-card {
        min-height: 300px;
        aspect-ratio: 3/4;
    }
}

@media (min-width: 1200px) {
    .portrait-card {
        min-height: 450px;
    }
}

/* Legacy Landscape Gallery Card Styles (for backward compatibility) */
.landscape-card {
    aspect-ratio: 16/9;
    min-height: 320px;
}

.landscape-card img,
.landscape-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
}

.landscape-card .text-center {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Responsive landscape adjustments */
@media (max-width: 768px) {
    .landscape-card {
        min-height: 250px;
        aspect-ratio: 4/3;
    }
}

@media (min-width: 1200px) {
    .landscape-card {
        min-height: 350px;
    }
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.9), rgba(255, 215, 0, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-content {
    text-align: center;
    color: var(--white);
    padding: 1rem;
}

.gallery-content h5 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.gallery-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

/* Apple-Style Contact Page */
.contact-info-section {
    background: var(--pure-white);
    padding: 100px 0;
}

.contact-card {
    background: var(--pure-white);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    height: 100%;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-blue);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
}

.contact-card:hover .contact-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.contact-icon i {
    font-size: 2rem;
    color: var(--pure-white);
}

.contact-card h4 {
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-card p {
    color: var(--primary-gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.contact-form-container {
    background: var(--pure-white);
    padding: 3rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.map-container {
    background: var(--pure-white);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.map-wrapper {
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-small);
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.info-item i {
    margin-right: 15px;
    margin-top: 5px;
    font-size: 1.2rem;
}

.info-content h6 {
    color: var(--chocolate-brown);
    margin-bottom: 0.5rem;
}

.info-content p {
    color: var(--dark-brown);
    font-size: 0.9rem;
    margin: 0;
}

.faq-section {
    background-color: var(--white);
}

.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(139, 69, 19, 0.1);
}

.accordion-button {
    background-color: var(--cream-white);
    color: var(--chocolate-brown);
    font-weight: 500;
    border: none;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--gold);
    color: var(--chocolate-brown);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    background-color: var(--white);
    color: var(--dark-brown);
    padding: 1.5rem;
}

/* Apple-Style Forms */
.form-control,
.form-select {
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-medium);
    padding: 12px 16px;
    transition: all var(--transition-fast);
    font-size: 1rem;
    background: var(--pure-white);
    color: var(--primary-black);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    outline: none;
}

.form-label {
    color: var(--primary-black);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

/* Apple-Style Modal */
.modal-content {
    border-radius: var(--radius-xl);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--pure-white);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: 24px;
}

.modal-title {
    color: var(--primary-black);
    font-weight: 600;
    font-size: 1.5rem;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    opacity: 0.6;
}

.btn-close:hover {
    opacity: 1;
}

/* Apple-Style Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 30px;
    }

    .hero-section {
        padding-top: 100px;
    }

    .navbar-brand .logo-img {
        height: 35px;
    }

    .navbar-brand .brand-text .main-text {
        font-size: 1.3rem;
    }

    .navbar-brand .brand-text .sub-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }

    .hero-section {
        padding-top: 80px;
        min-height: 80vh;
    }

    .navbar {
        padding: 8px 0;
    }

    .navbar-brand .logo-img {
        height: 32px;
        margin-right: 10px;
    }

    .navbar-brand .brand-text .main-text {
        font-size: 1.2rem;
    }

    .navbar-brand .brand-text .sub-text {
        font-size: 0.75rem;
    }

    .nav-link {
        padding: 12px 16px !important;
        margin: 4px 0;
        border-radius: var(--radius-medium);
    }

    .welcome-section,
    .services-preview,
    .gallery-preview,
    .our-story {
        padding: 60px 0;
    }

    .page-header {
        padding: 100px 0 60px;
        margin-top: 60px;
    }

    .service-card,
    .mission-card,
    .vision-card {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .footer {
        padding: 40px 0 20px;
    }

    .footer-logo .logo-img {
        height: 40px;
    }

    .footer-logo .brand-text .main-text {
        font-size: 1.3rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        min-height: 70vh;
        padding-top: 60px;
    }

    .btn-lg {
        padding: 14px 24px;
        font-size: 1rem;
        min-height: 48px;
    }

    .service-card {
        padding: 1.5rem 1rem;
    }

    .gallery-item img,
    .gallery-item > div {
        height: 220px;
    }

    .welcome-stats .col-4 {
        margin-bottom: 2rem;
    }

    .social-links a {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin-right: 8px;
    }
}

/* Apple-Style Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Apple-Style Scroll Behavior */
html {
    scroll-behavior: smooth;
}

/* Apple-Style Focus States */
*:focus {
    outline: 2px solid var(--accent-blue);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    outline: 2px solid var(--accent-blue);
    outline-offset: 2px;
}

/* Apple-Style Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    transition: opacity var(--transition-medium);
}
