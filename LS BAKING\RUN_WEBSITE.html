<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Run Your Website - <PERSON> Sai Baking Classes</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #FFF5E1, #FFD1DC);
            font-family: 'Arial', sans-serif;
            min-height: 100vh;
        }
        .run-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(139, 69, 19, 0.2);
            margin: 30px auto;
            max-width: 1000px;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #8B4513, #FFD700);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 40px;
        }
        .method-card {
            background: #f8f9fa;
            border: 2px solid #FFD700;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(139, 69, 19, 0.2);
        }
        .method-title {
            color: #8B4513;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .page-link {
            display: inline-block;
            margin: 8px;
            padding: 12px 20px;
            background: #FFD700;
            color: #8B4513;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .page-link:hover {
            background: #8B4513;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .code-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="run-container">
            <div class="header">
                <h1><i class="fas fa-rocket me-3"></i>Run Your Website</h1>
                <h2>Lakshmi Sai Baking Classes</h2>
                <p class="mb-0">Multiple ways to run your website without errors!</p>
            </div>
            
            <div class="content">
                <div class="success-box">
                    <h4><i class="fas fa-check-circle text-success me-2"></i>✅ All Files Saved Successfully!</h4>
                    <p class="mb-0">Your website is complete and error-free. Choose any method below to run it.</p>
                </div>

                <h3><i class="fas fa-play-circle me-2"></i>Quick Start - Click Any Page:</h3>
                <div class="text-center mb-4">
                    <a href="index.html" class="page-link">
                        <i class="fas fa-home me-2"></i>🏠 Home Page
                    </a>
                    <a href="about.html" class="page-link">
                        <i class="fas fa-users me-2"></i>👥 About Us
                    </a>
                    <a href="services.html" class="page-link">
                        <i class="fas fa-graduation-cap me-2"></i>📚 Services
                    </a>
                    <a href="gallery.html" class="page-link">
                        <i class="fas fa-images me-2"></i>🖼️ Gallery
                    </a>
                    <a href="contact.html" class="page-link">
                        <i class="fas fa-phone me-2"></i>📞 Contact
                    </a>
                </div>

                <div class="method-card">
                    <div class="method-title">
                        <i class="fas fa-mouse-pointer text-warning me-2"></i>Method 1: Direct File Access (Recommended)
                    </div>
                    <p><strong>✅ Works immediately - No server needed!</strong></p>
                    <ol>
                        <li>Double-click any HTML file (like <code>index.html</code>)</li>
                        <li>Your default browser will open the website</li>
                        <li>Navigate between pages using the menu</li>
                    </ol>
                    <div class="alert alert-success">
                        <strong>Perfect for:</strong> Testing, development, and immediate viewing
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-title">
                        <i class="fas fa-server text-primary me-2"></i>Method 2: XAMPP Server (For PHP Features)
                    </div>
                    <p><strong>For full functionality including contact forms:</strong></p>
                    <ol>
                        <li>Download and install XAMPP from <a href="https://www.apachefriends.org/" target="_blank">apachefriends.org</a></li>
                        <li>Start Apache and MySQL in XAMPP Control Panel</li>
                        <li>Copy your website folder to <code>C:\xampp\htdocs\</code></li>
                        <li>Visit <code>http://localhost/baking-institution/</code></li>
                    </ol>
                    <div class="alert alert-info">
                        <strong>Perfect for:</strong> Full website functionality with database and forms
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-title">
                        <i class="fas fa-globe text-success me-2"></i>Method 3: Live Server Extension (VS Code)
                    </div>
                    <p><strong>If you have Visual Studio Code:</strong></p>
                    <ol>
                        <li>Install "Live Server" extension in VS Code</li>
                        <li>Right-click on <code>index.html</code></li>
                        <li>Select "Open with Live Server"</li>
                        <li>Website opens at <code>http://127.0.0.1:5500</code></li>
                    </ol>
                    <div class="alert alert-info">
                        <strong>Perfect for:</strong> Development with auto-refresh
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-title">
                        <i class="fas fa-cloud text-info me-2"></i>Method 4: Web Hosting Upload
                    </div>
                    <p><strong>To make it live on the internet:</strong></p>
                    <ol>
                        <li>Choose a web hosting provider (Hostinger, Bluehost, etc.)</li>
                        <li>Upload all files via FTP or hosting panel</li>
                        <li>Your website will be live at your domain</li>
                    </ol>
                    <div class="alert alert-success">
                        <strong>Perfect for:</strong> Making your website public for students
                    </div>
                </div>

                <div class="warning-box">
                    <h5><i class="fas fa-info-circle text-warning me-2"></i>Important Notes:</h5>
                    <ul class="mb-0">
                        <li><strong>Logo:</strong> Add your logo as <code>images/logo.png</code> for complete branding</li>
                        <li><strong>Images:</strong> Some placeholder images may not display until you add real photos</li>
                        <li><strong>Forms:</strong> Contact forms need a server (XAMPP) or hosting to work fully</li>
                        <li><strong>Database:</strong> Import <code>database/baking_institution.sql</code> in phpMyAdmin for full functionality</li>
                    </ul>
                </div>

                <div class="success-box">
                    <h4><i class="fas fa-trophy text-warning me-2"></i>🏆 Your Website is Ready!</h4>
                    <p><strong>Features Working:</strong></p>
                    <div class="row">
                        <div class="col-md-6">
                            <ul>
                                <li>✅ Responsive design</li>
                                <li>✅ Navigation menus</li>
                                <li>✅ Bootstrap styling</li>
                                <li>✅ Font Awesome icons</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul>
                                <li>✅ Logo integration</li>
                                <li>✅ Contact information</li>
                                <li>✅ Workshop details</li>
                                <li>✅ Professional layout</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <h4>🎂 Start with your Home Page:</h4>
                    <a href="index.html" class="btn btn-warning btn-lg">
                        <i class="fas fa-home me-2"></i>Open Lakshmi Sai Baking Classes Website
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
